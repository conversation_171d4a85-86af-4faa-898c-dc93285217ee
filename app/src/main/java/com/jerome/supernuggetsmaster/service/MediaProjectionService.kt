package com.jerome.supernuggetsmaster.service

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Context
import android.content.Intent
import android.media.projection.MediaProjection
import android.os.Build
import android.os.IBinder
import androidx.core.app.NotificationCompat
import com.jerome.supernuggetsmaster.MainActivity
import com.jerome.supernuggetsmaster.R

/**
 * 媒体投影前台服务
 * Android 要求媒体投影必须在前台服务中运行
 */
class MediaProjectionService : Service() {
    
    companion object {
        private const val NOTIFICATION_ID = 1001
        private const val CHANNEL_ID = "media_projection_channel"
        private const val CHANNEL_NAME = "屏幕录制服务"

        const val ACTION_START = "ACTION_START"
        const val ACTION_STOP = "ACTION_STOP"
        const val EXTRA_MEDIA_PROJECTION = "EXTRA_MEDIA_PROJECTION"

        private var instance: MediaProjectionService? = null
        private var mediaProjection: MediaProjection? = null
        private var startCallback: (() -> Unit)? = null
        
        /**
         * 启动媒体投影服务
         */
        fun start(context: Context, mediaProjection: MediaProjection?, callback: (() -> Unit)? = null) {
            val intent = Intent(context, MediaProjectionService::class.java).apply {
                action = ACTION_START
            }
            this.mediaProjection = mediaProjection
            this.startCallback = callback

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(intent)
            } else {
                context.startService(intent)
            }
        }

        /**
         * 设置媒体投影实例
         */
        fun setMediaProjection(mediaProjection: MediaProjection) {
            this.mediaProjection = mediaProjection
        }
        
        /**
         * 停止媒体投影服务
         */
        fun stop(context: Context) {
            val intent = Intent(context, MediaProjectionService::class.java).apply {
                action = ACTION_STOP
            }
            context.startService(intent)
        }
        
        /**
         * 获取当前的媒体投影实例
         */
        fun getMediaProjection(): MediaProjection? = mediaProjection

        /**
         * 检查 MediaProjection 是否有效
         */
        fun isMediaProjectionValid(): Boolean {
            return try {
                mediaProjection != null && instance != null
            } catch (e: Exception) {
                false
            }
        }

        /**
         * 检查服务是否正在运行
         */
        fun isRunning(): Boolean = instance != null
    }
    
    override fun onCreate() {
        super.onCreate()
        instance = this
        createNotificationChannel()
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        when (intent?.action) {
            ACTION_START -> {
                startForegroundService()
                return START_STICKY
            }
            ACTION_STOP -> {
                stopForegroundService()
                return START_NOT_STICKY
            }
        }
        return START_NOT_STICKY
    }
    
    override fun onBind(intent: Intent?): IBinder? = null
    
    override fun onDestroy() {
        super.onDestroy()
        instance = null
        mediaProjection?.stop()
        mediaProjection = null
    }
    
    /**
     * 启动前台服务
     */
    private fun startForegroundService() {
        val notification = createNotification()
        startForeground(NOTIFICATION_ID, notification)

        // 通知服务已启动
        startCallback?.invoke()
        startCallback = null
    }
    
    /**
     * 停止前台服务
     */
    private fun stopForegroundService() {
        stopForeground(true)
        stopSelf()
    }
    
    /**
     * 创建通知渠道
     */
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                CHANNEL_NAME,
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "用于屏幕录制和图像识别功能"
                setShowBadge(false)
            }
            
            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }
    
    /**
     * 创建前台服务通知
     */
    private fun createNotification(): Notification {
        val intent = Intent(this, MainActivity::class.java).apply {
            // 防止启动新的Activity实例
            flags = Intent.FLAG_ACTIVITY_SINGLE_TOP or Intent.FLAG_ACTIVITY_CLEAR_TOP
        }
        val pendingIntent = PendingIntent.getActivity(
            this,
            0,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("SuperNuggets Master")
            .setContentText("屏幕录制服务正在运行")
            .setSmallIcon(R.drawable.ic_launcher_foreground)
            // 暂时不设置点击意图，避免意外启动新Activity
            // .setContentIntent(pendingIntent)
            .setOngoing(true)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setCategory(NotificationCompat.CATEGORY_SERVICE)
            .setAutoCancel(false)
            .build()
    }
}
