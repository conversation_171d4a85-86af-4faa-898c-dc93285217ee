package com.jerome.supernuggetsmaster.ui

import androidx.compose.animation.*
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.CheckCircle
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material.icons.outlined.Favorite
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.runtime.remember
import androidx.fragment.app.FragmentActivity
import com.jerome.supernuggetsmaster.data.ScriptExecutionStatus
import com.jerome.supernuggetsmaster.data.ScriptInfo
import com.jerome.supernuggetsmaster.permission.PermissionManager
import com.jerome.supernuggetsmaster.utils.MediaProjectionHelper

/**
 * 权限设置页面 - 米家风格
 */
@Composable
fun PermissionSetupScreen(
    modifier: Modifier = Modifier,
    permissionStatus: PermissionManager.PermissionStatus,
    permissionManager: PermissionManager,
    mediaProjectionHelper: MediaProjectionHelper? = null,
    activity: FragmentActivity? = null
) {
    Column(
        modifier = modifier
            .background(MaterialTheme.colorScheme.background)
            .padding(20.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(24.dp)
    ) {
        Spacer(modifier = Modifier.height(40.dp))
        
        // 欢迎标题
        Text(
            text = "🚀 欢迎使用智能助手",
            fontSize = 24.sp,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.primary,
            textAlign = TextAlign.Center
        )
        
        Text(
            text = "为了正常使用，需要开启以下权限",
            fontSize = 16.sp,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            textAlign = TextAlign.Center
        )
        
        Spacer(modifier = Modifier.height(20.dp))
        
        // 权限卡片列表
        MijiaPermissionCard(
            title = "无障碍服务",
            description = "用于自动化操作应用界面",
            icon = "🔧",
            isGranted = permissionStatus.accessibilityEnabled,
            onRequest = { permissionManager.requestAccessibilityPermission() }
        )
        
        MijiaPermissionCard(
            title = "悬浮窗权限",
            description = "显示运行状态和控制面板",
            icon = "📱",
            isGranted = permissionStatus.overlayEnabled,
            onRequest = { permissionManager.requestOverlayPermission() }
        )

        // 媒体投影权限卡片
        if (mediaProjectionHelper != null && activity != null) {
            MediaProjectionPermissionCard(
                title = "屏幕录制权限",
                description = "用于图像识别和屏幕截图",
                icon = "📸",
                isGranted = permissionStatus.mediaProjectionEnabled,
                mediaProjectionHelper = mediaProjectionHelper,
                activity = activity
            )
        }
        
        Spacer(modifier = Modifier.weight(1f))
        
        // 完成状态提示
        if (permissionStatus.allPermissionsGranted) {
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer
                ),
                shape = RoundedCornerShape(16.dp)
            ) {
                Column(
                    modifier = Modifier.padding(20.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = "🎉 设置完成！",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onPrimaryContainer
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "所有权限已开启，可以开始使用脚本功能",
                        fontSize = 14.sp,
                        color = MaterialTheme.colorScheme.onPrimaryContainer,
                        textAlign = TextAlign.Center
                    )
                }
            }
        }
    }
}

/**
 * 米家风格权限卡片
 */
@Composable
fun MijiaPermissionCard(
    title: String,
    description: String,
    icon: String,
    isGranted: Boolean,
    onRequest: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { if (!isGranted) onRequest() },
        colors = CardDefaults.cardColors(
            containerColor = if (isGranted) {
                MaterialTheme.colorScheme.primaryContainer
            } else {
                MaterialTheme.colorScheme.surface
            }
        ),
        shape = RoundedCornerShape(16.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(20.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 图标
            Box(
                modifier = Modifier
                    .size(48.dp)
                    .background(
                        color = if (isGranted) {
                            MaterialTheme.colorScheme.primary.copy(alpha = 0.1f)
                        } else {
                            MaterialTheme.colorScheme.outline.copy(alpha = 0.1f)
                        },
                        shape = RoundedCornerShape(12.dp)
                    ),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = icon,
                    fontSize = 24.sp
                )
            }
            
            Spacer(modifier = Modifier.width(16.dp))
            
            // 内容
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = title,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colorScheme.onSurface
                )
                Spacer(modifier = Modifier.height(4.dp))
                Text(
                    text = description,
                    fontSize = 14.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            
            // 状态指示器
            if (isGranted) {
                Icon(
                    imageVector = Icons.Filled.CheckCircle,
                    contentDescription = "已授权",
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(24.dp)
                )
            } else {
                Button(
                    onClick = onRequest,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.primary
                    ),
                    shape = RoundedCornerShape(8.dp)
                ) {
                    Text(
                        text = "开启",
                        fontSize = 12.sp
                    )
                }
            }
        }
    }
}

/**
 * 米家风格脚本列表页面
 */
@Composable
fun MijiaScriptListScreen(
    modifier: Modifier = Modifier,
    scripts: List<ScriptInfo>,
    executionStatus: ScriptExecutionStatus,
    currentScript: ScriptInfo?,
    executionProgress: Float,
    onScriptToggle: (String) -> Unit
) {
    LazyColumn(
        modifier = modifier
            .background(MaterialTheme.colorScheme.background)
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        // 顶部状态卡片
        item {
            MijiaStatusCard(
                executionStatus = executionStatus,
                currentScript = currentScript,
                executionProgress = executionProgress
            )
        }
        
        // 脚本列表标题
        item {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 8.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "可用脚本",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onBackground
                )
                Text(
                    text = "${scripts.count { it.isSelected }}/${scripts.size} 已选择",
                    fontSize = 14.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
        
        // 脚本列表
        items(scripts) { script ->
            MijiaScriptCard(
                script = script,
                isRunning = currentScript?.id == script.id,
                onToggle = { onScriptToggle(script.id) }
            )
        }
        
        // 底部空白
        item {
            Spacer(modifier = Modifier.height(100.dp))
        }
    }
}

/**
 * 米家风格状态卡片
 */
@Composable
fun MijiaStatusCard(
    executionStatus: ScriptExecutionStatus,
    currentScript: ScriptInfo?,
    executionProgress: Float
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = when (executionStatus) {
                ScriptExecutionStatus.IDLE -> MaterialTheme.colorScheme.surfaceVariant
                ScriptExecutionStatus.PREPARING, ScriptExecutionStatus.RUNNING -> MaterialTheme.colorScheme.primaryContainer
                ScriptExecutionStatus.COMPLETED -> MaterialTheme.colorScheme.secondaryContainer
                ScriptExecutionStatus.FAILED, ScriptExecutionStatus.STOPPED -> MaterialTheme.colorScheme.errorContainer
            }
        ),
        shape = RoundedCornerShape(16.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                val statusText = when (executionStatus) {
                    ScriptExecutionStatus.IDLE -> "🏠 系统空闲"
                    ScriptExecutionStatus.PREPARING -> "⚡ 准备执行"
                    ScriptExecutionStatus.RUNNING -> "🚀 正在执行"
                    ScriptExecutionStatus.COMPLETED -> "✅ 执行完成"
                    ScriptExecutionStatus.FAILED -> "❌ 执行失败"
                    ScriptExecutionStatus.STOPPED -> "⏹️ 已停止"
                }
                
                Text(
                    text = statusText,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold
                )
            }
            
            if (currentScript != null) {
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "当前脚本：${currentScript.icon} ${currentScript.name}",
                    fontSize = 14.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            
            if (executionStatus == ScriptExecutionStatus.RUNNING && executionProgress > 0) {
                Spacer(modifier = Modifier.height(12.dp))
                LinearProgressIndicator(
                    progress = executionProgress,
                    modifier = Modifier.fillMaxWidth(),
                    color = MaterialTheme.colorScheme.primary,
                    trackColor = MaterialTheme.colorScheme.outline.copy(alpha = 0.3f)
                )
                Spacer(modifier = Modifier.height(4.dp))
                Text(
                    text = "进度：${(executionProgress * 100).toInt()}%",
                    fontSize = 12.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

/**
 * 米家风格脚本卡片
 */
@Composable
fun MijiaScriptCard(
    script: ScriptInfo,
    isRunning: Boolean,
    onToggle: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onToggle() },
        colors = CardDefaults.cardColors(
            containerColor = if (script.isSelected) {
                MaterialTheme.colorScheme.primaryContainer
            } else {
                MaterialTheme.colorScheme.surface
            }
        ),
        shape = RoundedCornerShape(12.dp),
        elevation = CardDefaults.cardElevation(
            defaultElevation = if (script.isSelected) 4.dp else 2.dp
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 选择指示器
            Icon(
                imageVector = if (script.isSelected) {
                    Icons.Filled.CheckCircle
                } else {
                    Icons.Outlined.Favorite
                },
                contentDescription = if (script.isSelected) "已选择" else "未选择",
                tint = if (script.isSelected) {
                    MaterialTheme.colorScheme.primary
                } else {
                    MaterialTheme.colorScheme.outline
                },
                modifier = Modifier.size(24.dp)
            )
            
            Spacer(modifier = Modifier.width(16.dp))
            
            // 应用图标
            Box(
                modifier = Modifier
                    .size(40.dp)
                    .background(
                        color = MaterialTheme.colorScheme.outline.copy(alpha = 0.1f),
                        shape = RoundedCornerShape(8.dp)
                    ),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = script.icon,
                    fontSize = 20.sp
                )
            }
            
            Spacer(modifier = Modifier.width(16.dp))
            
            // 脚本信息
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = script.name,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colorScheme.onSurface
                )
                Spacer(modifier = Modifier.height(4.dp))
                Text(
                    text = script.description,
                    fontSize = 13.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
                Spacer(modifier = Modifier.height(2.dp))
                Text(
                    text = "预计 ${script.estimatedDuration / 60}分${script.estimatedDuration % 60}秒",
                    fontSize = 11.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            
            // 运行状态指示器
            if (isRunning) {
                Box(
                    modifier = Modifier
                        .size(8.dp)
                        .background(
                            color = MaterialTheme.colorScheme.primary,
                            shape = RoundedCornerShape(4.dp)
                        )
                )
            }
        }
    }
}

/**
 * 米家风格底部操作栏
 */
@Composable
fun MijiaBottomActionBar(
    selectedCount: Int,
    executionStatus: ScriptExecutionStatus,
    onStartExecution: () -> Unit,
    onStopExecution: () -> Unit,
    onSelectAll: () -> Unit,
    onReset: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        shape = RoundedCornerShape(topStart = 20.dp, topEnd = 20.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(20.dp),
            horizontalArrangement = Arrangement.spacedBy(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 全选按钮
            OutlinedButton(
                onClick = onSelectAll,
                modifier = Modifier.weight(1f),
                colors = ButtonDefaults.outlinedButtonColors(
                    contentColor = MaterialTheme.colorScheme.primary
                ),
                shape = RoundedCornerShape(12.dp)
            ) {
                Text(
                    text = "全选",
                    fontSize = 14.sp
                )
            }
            
            // 主操作按钮
            when (executionStatus) {
                ScriptExecutionStatus.IDLE -> {
                    Button(
                        onClick = onStartExecution,
                        enabled = selectedCount > 0,
                        modifier = Modifier.weight(2f),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = MaterialTheme.colorScheme.primary
                        ),
                        shape = RoundedCornerShape(12.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Filled.PlayArrow,
                            contentDescription = "开始执行",
                            modifier = Modifier.size(18.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = if (selectedCount > 0) "执行脚本 ($selectedCount)" else "选择脚本",
                            fontSize = 14.sp
                        )
                    }
                }
                
                ScriptExecutionStatus.PREPARING, ScriptExecutionStatus.RUNNING -> {
                    Button(
                        onClick = onStopExecution,
                        modifier = Modifier.weight(2f),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = MaterialTheme.colorScheme.error
                        ),
                        shape = RoundedCornerShape(12.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Filled.Warning,
                            contentDescription = "停止执行",
                            modifier = Modifier.size(18.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = "停止执行",
                            fontSize = 14.sp
                        )
                    }
                }
                
                ScriptExecutionStatus.COMPLETED, ScriptExecutionStatus.FAILED, ScriptExecutionStatus.STOPPED -> {
                    Button(
                        onClick = onReset,
                        modifier = Modifier.weight(2f),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = MaterialTheme.colorScheme.primary
                        ),
                        shape = RoundedCornerShape(12.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Filled.Refresh,
                            contentDescription = "重新开始",
                            modifier = Modifier.size(18.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = "重新开始",
                            fontSize = 14.sp
                        )
                    }
                }
            }
        }
    }
}

/**
 * 媒体投影权限卡片
 * 需要特殊处理的权限请求
 */
@Composable
fun MediaProjectionPermissionCard(
    title: String,
    description: String,
    icon: String,
    isGranted: Boolean,
    mediaProjectionHelper: MediaProjectionHelper,
    activity: FragmentActivity
) {
    MijiaPermissionCard(
        title = title,
        description = description,
        icon = icon,
        isGranted = isGranted,
        onRequest = {
            android.util.Log.i("MediaProjectionCard", "请求媒体投影权限")
            mediaProjectionHelper.requestPermission()
        }
    )
}