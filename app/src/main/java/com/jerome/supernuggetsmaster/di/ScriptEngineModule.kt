package com.jerome.supernuggetsmaster.di

import android.content.Context
import com.jerome.supernuggetsmaster.engine.AppLauncher
import com.jerome.supernuggetsmaster.engine.PageRecognizer
import com.jerome.supernuggetsmaster.engine.ScriptEngine
import com.jerome.supernuggetsmaster.utils.Logger
import com.jerome.supernuggetsmaster.utils.ImageRecognitionHelper
import com.jerome.supernuggetsmaster.utils.ScreenshotHelper
import com.jerome.supernuggetsmaster.utils.OpenCVInitializer
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object ScriptEngineModule {
    
    @Provides
    @Singleton
    fun providePageRecognizer(
        @ApplicationContext context: Context,
        logger: Logger,
        imageRecognitionHelper: ImageRecognition<PERSON>elper,
        screenshotHelper: ScreenshotHelper,
        openCVInitializer: OpenCVInitializer
    ): PageRecognizer {
        return PageRecognizer(context, logger, imageRecognitionHelper, screenshotHelper, openCVInitializer)
    }
    
    @Provides
    @Singleton
    fun provideAppLauncher(
        @ApplicationContext context: Context,
        logger: Logger
    ): AppLauncher {
        return AppLauncher(context, logger)
    }
    
    @Provides
    @Singleton
    fun provideScriptEngine(
        @ApplicationContext context: Context,
        logger: Logger,
        pageRecognizer: PageRecognizer,
        appLauncher: AppLauncher
    ): ScriptEngine {
        return ScriptEngine(context, logger, pageRecognizer, appLauncher)
    }
} 