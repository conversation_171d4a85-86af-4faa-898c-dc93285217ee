package com.jerome.supernuggetsmaster.utils

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 图像识别测试工具
 * 用于测试和验证图像识别功能
 */
@Singleton
class ImageRecognitionTester @Inject constructor(
    private val context: Context,
    private val logger: Logger,
    private val imageRecognitionHelper: ImageRecognitionHelper,
    private val screenshotHelper: ScreenshotHelper,
    private val openCVInitializer: OpenCVInitializer
) {
    
    /**
     * 测试结果
     */
    data class TestResult(
        val templatePath: String,
        val isMatch: Boolean,
        val confidence: Double,
        val adjustedThreshold: Float,
        val screenSize: String,
        val templateSize: String,
        val scaleInfo: String,
        val executionTime: Long
    )
    
    /**
     * 测试单个模板的识别效果
     */
    suspend fun testTemplateRecognition(templatePath: String): TestResult? {
        return withContext(Dispatchers.IO) {
            try {
                val startTime = System.currentTimeMillis()
                
                // 确保OpenCV已初始化
                if (!openCVInitializer.isInitialized()) {
                    logger.w("ImageRecognitionTester", "OpenCV未初始化，无法进行测试")
                    return@withContext null
                }
                
                // 获取屏幕截图
                val screenBitmap = screenshotHelper.captureScreen()
                if (screenBitmap == null) {
                    logger.w("ImageRecognitionTester", "获取屏幕截图失败")
                    return@withContext null
                }
                
                // 加载模板图片
                val templateBitmap = loadTemplateFromAssets(templatePath)
                if (templateBitmap == null) {
                    logger.w("ImageRecognitionTester", "加载模板图片失败: $templatePath")
                    screenBitmap.recycle()
                    return@withContext null
                }
                
                // 执行识别
                val matchResult = imageRecognitionHelper.matchTemplate(screenBitmap, templatePath)
                
                val executionTime = System.currentTimeMillis() - startTime
                
                // 计算缩放信息
                val scaleX = screenBitmap.width.toFloat() / 1080f
                val scaleY = screenBitmap.height.toFloat() / 2340f
                
                val result = TestResult(
                    templatePath = templatePath,
                    isMatch = matchResult.isMatch,
                    confidence = matchResult.confidence,
                    adjustedThreshold = matchResult.adjustedThreshold,
                    screenSize = "${screenBitmap.width}x${screenBitmap.height}",
                    templateSize = "${templateBitmap.width}x${templateBitmap.height}",
                    scaleInfo = "X:${String.format("%.2f", scaleX)}, Y:${String.format("%.2f", scaleY)}",
                    executionTime = executionTime
                )
                
                // 释放资源
                screenBitmap.recycle()
                templateBitmap.recycle()
                
                logger.i("ImageRecognitionTester", 
                    "测试完成 - 模板: $templatePath, " +
                    "匹配: ${result.isMatch}, " +
                    "置信度: ${String.format("%.3f", result.confidence)}, " +
                    "阈值: ${String.format("%.3f", result.adjustedThreshold)}, " +
                    "耗时: ${result.executionTime}ms")
                
                result
                
            } catch (e: Exception) {
                logger.e("ImageRecognitionTester", "测试过程中发生异常: $templatePath", e)
                null
            }
        }
    }
    
    /**
     * 批量测试多个模板
     */
    suspend fun testMultipleTemplates(templatePaths: List<String>): List<TestResult> {
        val results = mutableListOf<TestResult>()
        
        logger.i("ImageRecognitionTester", "开始批量测试，模板数量: ${templatePaths.size}")
        
        for (templatePath in templatePaths) {
            val result = testTemplateRecognition(templatePath)
            if (result != null) {
                results.add(result)
            }
            
            // 添加短暂延迟，避免过于频繁的截图
            kotlinx.coroutines.delay(500)
        }
        
        logger.i("ImageRecognitionTester", "批量测试完成，成功测试: ${results.size}/${templatePaths.size}")
        
        return results
    }
    
    /**
     * 生成测试报告
     */
    fun generateTestReport(results: List<TestResult>): String {
        val report = StringBuilder()
        report.appendLine("=== 图像识别测试报告 ===")
        report.appendLine("测试时间: ${java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(java.util.Date())}")
        report.appendLine("测试数量: ${results.size}")
        report.appendLine()
        
        val matchCount = results.count { it.isMatch }
        val avgConfidence = results.map { it.confidence }.average()
        val avgExecutionTime = results.map { it.executionTime }.average()
        
        report.appendLine("=== 总体统计 ===")
        report.appendLine("匹配成功: $matchCount/${results.size} (${String.format("%.1f", matchCount * 100.0 / results.size)}%)")
        report.appendLine("平均置信度: ${String.format("%.3f", avgConfidence)}")
        report.appendLine("平均执行时间: ${String.format("%.1f", avgExecutionTime)}ms")
        report.appendLine()
        
        report.appendLine("=== 详细结果 ===")
        results.forEach { result ->
            report.appendLine("模板: ${result.templatePath}")
            report.appendLine("  匹配: ${if (result.isMatch) "✓" else "✗"}")
            report.appendLine("  置信度: ${String.format("%.3f", result.confidence)} (阈值: ${String.format("%.3f", result.adjustedThreshold)})")
            report.appendLine("  屏幕尺寸: ${result.screenSize}")
            report.appendLine("  模板尺寸: ${result.templateSize}")
            report.appendLine("  缩放比例: ${result.scaleInfo}")
            report.appendLine("  执行时间: ${result.executionTime}ms")
            report.appendLine()
        }
        
        return report.toString()
    }
    
    /**
     * 从assets加载模板图片
     */
    private fun loadTemplateFromAssets(templatePath: String): Bitmap? {
        return try {
            val inputStream = context.assets.open(templatePath)
            val bitmap = BitmapFactory.decodeStream(inputStream)
            inputStream.close()
            bitmap
        } catch (e: Exception) {
            logger.e("ImageRecognitionTester", "加载模板图片失败: $templatePath", e)
            null
        }
    }
    
    /**
     * 获取当前屏幕信息
     */
    suspend fun getCurrentScreenInfo(): String? {
        return try {
            val screenInfo = screenshotHelper.getScreenInfo()
            "屏幕信息: ${screenInfo.width}x${screenInfo.height}, 密度: ${screenInfo.density}"
        } catch (e: Exception) {
            logger.e("ImageRecognitionTester", "获取屏幕信息失败", e)
            null
        }
    }
}
