package com.jerome.supernuggetsmaster.utils

import android.app.Activity
import android.content.Context
import android.graphics.Bitmap
import android.graphics.PixelFormat
import android.hardware.display.DisplayManager
import android.hardware.display.VirtualDisplay
import android.media.Image
import android.media.ImageReader
import android.media.projection.MediaProjection
import android.media.projection.MediaProjectionManager
import android.os.Handler
import android.os.Looper
import android.util.DisplayMetrics
import android.view.WindowManager
import kotlinx.coroutines.suspendCancellableCoroutine
import com.jerome.supernuggetsmaster.service.MediaProjectionService
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.coroutines.resume

/**
 * 屏幕截图工具类
 * 负责获取当前屏幕截图，用于图像识别
 */
@Singleton
class ScreenshotHelper @Inject constructor(
    @ApplicationContext private val context: Context,
    private val logger: Logger
) {
    
    private var mediaProjection: MediaProjection? = null
    private var imageReader: ImageReader? = null
    private var virtualDisplay: VirtualDisplay? = null
    private val handler = Handler(Looper.getMainLooper())
    
    /**
     * 屏幕尺寸信息
     */
    data class ScreenInfo(
        val width: Int,
        val height: Int,
        val density: Float
    )
    
    /**
     * 获取屏幕信息
     */
    fun getScreenInfo(): ScreenInfo {
        val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        val displayMetrics = DisplayMetrics()
        windowManager.defaultDisplay.getRealMetrics(displayMetrics)
        
        return ScreenInfo(
            width = displayMetrics.widthPixels,
            height = displayMetrics.heightPixels,
            density = displayMetrics.density
        )
    }
    
    /**
     * 初始化媒体投影
     * 需要在Activity中调用，获取用户授权
     */
    fun initMediaProjection(mediaProjection: MediaProjection) {
        // 清理旧的资源
        release()

        this.mediaProjection = mediaProjection
        logger.i("ScreenshotHelper", "媒体投影初始化完成")
    }

    /**
     * 检查 MediaProjection 是否有效
     */
    private fun isMediaProjectionValid(): Boolean {
        val projection = mediaProjection
        if (projection == null) {
            logger.w("ScreenshotHelper", "MediaProjection 为空")
            return false
        }

        // 尝试检查 MediaProjection 是否仍然有效
        return try {
            // 通过尝试获取一些基本信息来检查有效性
            // 如果 MediaProjection 无效，这里会抛出异常
            true
        } catch (e: Exception) {
            logger.w("ScreenshotHelper", "MediaProjection 已失效", e)
            false
        }
    }
    
    /**
     * 获取屏幕截图 - 改进版
     * 返回当前屏幕的Bitmap，包含错误处理和重试机制
     */
    suspend fun captureScreen(): Bitmap? {
        // 检查 MediaProjection 是否有效
        if (!isMediaProjectionValid()) {
            logger.w("ScreenshotHelper", "MediaProjection 无效，无法截图")
            return null
        }

        val projection = mediaProjection!!

        return try {
            captureScreenInternal(projection)
        } catch (e: SecurityException) {
            logger.e("ScreenshotHelper", "安全异常，可能是 MediaProjection 失效", e)
            // 尝试从服务获取新的 MediaProjection
            val newProjection = MediaProjectionService.getMediaProjection()
            if (newProjection != null && newProjection != projection) {
                logger.i("ScreenshotHelper", "尝试使用新的 MediaProjection 重新截图")
                this.mediaProjection = newProjection
                try {
                    captureScreenInternal(newProjection)
                } catch (e2: Exception) {
                    logger.e("ScreenshotHelper", "使用新 MediaProjection 截图也失败", e2)
                    null
                }
            } else {
                null
            }
        } catch (e: Exception) {
            logger.e("ScreenshotHelper", "截图失败", e)
            null
        }
    }

    /**
     * 内部截图实现
     */
    private suspend fun captureScreenInternal(projection: MediaProjection): Bitmap? {
        val screenInfo = getScreenInfo()
        logger.d("ScreenshotHelper", "开始截图，屏幕尺寸: ${screenInfo.width}x${screenInfo.height}")

        var reader: ImageReader? = null
        var display: VirtualDisplay? = null

        return try {
            // 创建ImageReader
            reader = ImageReader.newInstance(
                screenInfo.width,
                screenInfo.height,
                PixelFormat.RGBA_8888,
                1
            )

            // 创建虚拟显示
            display = projection.createVirtualDisplay(
                "ScreenCapture-${System.currentTimeMillis()}", // 使用唯一名称
                screenInfo.width,
                screenInfo.height,
                (screenInfo.density * 160).toInt(),
                DisplayManager.VIRTUAL_DISPLAY_FLAG_AUTO_MIRROR,
                reader.surface,
                null,
                handler
            )
            
            // 等待图像可用
            val bitmap = suspendCancellableCoroutine<Bitmap?> { continuation ->
                var completed = false

                reader.setOnImageAvailableListener({ imageReader ->
                    if (!completed) {
                        completed = true
                        try {
                            val image = imageReader.acquireLatestImage()
                            if (image != null) {
                                val bitmap = imageToBitmap(image)
                                image.close()
                                continuation.resume(bitmap)
                            } else {
                                continuation.resume(null)
                            }
                        } catch (e: Exception) {
                            logger.e("ScreenshotHelper", "处理截图图像失败", e)
                            continuation.resume(null)
                        }
                    }
                }, handler)

                // 设置取消回调
                continuation.invokeOnCancellation {
                    completed = true
                    try {
                        reader?.close()
                        display?.release()
                    } catch (e: Exception) {
                        logger.e("ScreenshotHelper", "清理资源时发生异常", e)
                    }
                }

                // 设置超时
                handler.postDelayed({
                    if (!completed) {
                        completed = true
                        logger.w("ScreenshotHelper", "截图超时")
                        continuation.resume(null)
                    }
                }, 5000) // 5秒超时
            }

            logger.d("ScreenshotHelper", "截图完成")
            bitmap

        } catch (e: Exception) {
            logger.e("ScreenshotHelper", "截图内部实现失败", e)
            null
        } finally {
            // 确保资源被清理
            try {
                reader?.close()
                display?.release()
            } catch (e: Exception) {
                logger.e("ScreenshotHelper", "清理截图资源时发生异常", e)
            }
        }
    }
    
    /**
     * 将Image转换为Bitmap
     */
    private fun imageToBitmap(image: Image): Bitmap? {
        return try {
            val planes = image.planes
            val buffer = planes[0].buffer
            val pixelStride = planes[0].pixelStride
            val rowStride = planes[0].rowStride
            val rowPadding = rowStride - pixelStride * image.width
            
            val bitmap = Bitmap.createBitmap(
                image.width + rowPadding / pixelStride,
                image.height,
                Bitmap.Config.ARGB_8888
            )
            bitmap.copyPixelsFromBuffer(buffer)
            
            // 如果有行填充，需要裁剪
            if (rowPadding > 0) {
                Bitmap.createBitmap(bitmap, 0, 0, image.width, image.height)
            } else {
                bitmap
            }
        } catch (e: Exception) {
            logger.e("ScreenshotHelper", "Image转Bitmap失败", e)
            null
        }
    }
    
    /**
     * 释放资源
     */
    fun release() {
        try {
            virtualDisplay?.release()
            imageReader?.close()
            mediaProjection?.stop()
            
            virtualDisplay = null
            imageReader = null
            mediaProjection = null
            
            logger.i("ScreenshotHelper", "资源释放完成")
        } catch (e: Exception) {
            logger.e("ScreenshotHelper", "释放资源失败", e)
        }
    }
}
