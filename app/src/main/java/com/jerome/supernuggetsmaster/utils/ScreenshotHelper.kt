package com.jerome.supernuggetsmaster.utils

import android.app.Activity
import android.content.Context
import android.graphics.Bitmap
import android.graphics.PixelFormat
import android.hardware.display.DisplayManager
import android.hardware.display.VirtualDisplay
import android.media.Image
import android.media.ImageReader
import android.media.projection.MediaProjection
import android.media.projection.MediaProjectionManager
import android.os.Handler
import android.os.Looper
import android.util.DisplayMetrics
import android.view.WindowManager
import kotlinx.coroutines.suspendCancellableCoroutine
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.coroutines.resume

/**
 * 屏幕截图工具类
 * 负责获取当前屏幕截图，用于图像识别
 */
@Singleton
class ScreenshotHelper @Inject constructor(
    private val context: Context,
    private val logger: Logger
) {
    
    private var mediaProjection: MediaProjection? = null
    private var imageReader: ImageReader? = null
    private var virtualDisplay: VirtualDisplay? = null
    private val handler = Handler(Looper.getMainLooper())
    
    /**
     * 屏幕尺寸信息
     */
    data class ScreenInfo(
        val width: Int,
        val height: Int,
        val density: Float
    )
    
    /**
     * 获取屏幕信息
     */
    fun getScreenInfo(): ScreenInfo {
        val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        val displayMetrics = DisplayMetrics()
        windowManager.defaultDisplay.getRealMetrics(displayMetrics)
        
        return ScreenInfo(
            width = displayMetrics.widthPixels,
            height = displayMetrics.heightPixels,
            density = displayMetrics.density
        )
    }
    
    /**
     * 初始化媒体投影
     * 需要在Activity中调用，获取用户授权
     */
    fun initMediaProjection(mediaProjection: MediaProjection) {
        this.mediaProjection = mediaProjection
        logger.i("ScreenshotHelper", "媒体投影初始化完成")
    }
    
    /**
     * 获取屏幕截图
     * 返回当前屏幕的Bitmap
     */
    suspend fun captureScreen(): Bitmap? {
        val projection = mediaProjection
        if (projection == null) {
            logger.w("ScreenshotHelper", "媒体投影未初始化，无法截图")
            return null
        }
        
        return try {
            val screenInfo = getScreenInfo()
            logger.d("ScreenshotHelper", "开始截图，屏幕尺寸: ${screenInfo.width}x${screenInfo.height}")
            
            // 创建ImageReader
            val reader = ImageReader.newInstance(
                screenInfo.width,
                screenInfo.height,
                PixelFormat.RGBA_8888,
                1
            )
            
            // 创建虚拟显示
            val display = projection.createVirtualDisplay(
                "ScreenCapture",
                screenInfo.width,
                screenInfo.height,
                (screenInfo.density * 160).toInt(),
                DisplayManager.VIRTUAL_DISPLAY_FLAG_AUTO_MIRROR,
                reader.surface,
                null,
                handler
            )
            
            // 等待图像可用
            val bitmap = suspendCancellableCoroutine<Bitmap?> { continuation ->
                reader.setOnImageAvailableListener({ imageReader ->
                    try {
                        val image = imageReader.acquireLatestImage()
                        if (image != null) {
                            val bitmap = imageToBitmap(image)
                            image.close()
                            continuation.resume(bitmap)
                        } else {
                            continuation.resume(null)
                        }
                    } catch (e: Exception) {
                        logger.e("ScreenshotHelper", "处理截图图像失败", e)
                        continuation.resume(null)
                    }
                }, handler)
                
                // 设置取消回调
                continuation.invokeOnCancellation {
                    reader.close()
                    display.release()
                }
            }
            
            // 清理资源
            reader.close()
            display.release()
            
            logger.d("ScreenshotHelper", "截图完成")
            bitmap
            
        } catch (e: Exception) {
            logger.e("ScreenshotHelper", "截图失败", e)
            null
        }
    }
    
    /**
     * 将Image转换为Bitmap
     */
    private fun imageToBitmap(image: Image): Bitmap? {
        return try {
            val planes = image.planes
            val buffer = planes[0].buffer
            val pixelStride = planes[0].pixelStride
            val rowStride = planes[0].rowStride
            val rowPadding = rowStride - pixelStride * image.width
            
            val bitmap = Bitmap.createBitmap(
                image.width + rowPadding / pixelStride,
                image.height,
                Bitmap.Config.ARGB_8888
            )
            bitmap.copyPixelsFromBuffer(buffer)
            
            // 如果有行填充，需要裁剪
            if (rowPadding > 0) {
                Bitmap.createBitmap(bitmap, 0, 0, image.width, image.height)
            } else {
                bitmap
            }
        } catch (e: Exception) {
            logger.e("ScreenshotHelper", "Image转Bitmap失败", e)
            null
        }
    }
    
    /**
     * 释放资源
     */
    fun release() {
        try {
            virtualDisplay?.release()
            imageReader?.close()
            mediaProjection?.stop()
            
            virtualDisplay = null
            imageReader = null
            mediaProjection = null
            
            logger.i("ScreenshotHelper", "资源释放完成")
        } catch (e: Exception) {
            logger.e("ScreenshotHelper", "释放资源失败", e)
        }
    }
}
