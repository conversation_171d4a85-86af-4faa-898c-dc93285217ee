package com.jerome.supernuggetsmaster.utils

import kotlinx.coroutines.*
import com.jerome.supernuggetsmaster.permission.PermissionManager
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 权限状态监听器
 * 定期检查权限状态并更新UI
 */
@Singleton
class PermissionStatusMonitor @Inject constructor(
    private val permissionManager: PermissionManager,
    private val logger: Logger,
    private val mediaProjectionHelper: MediaProjectionHelper
) {
    
    private var monitorJob: Job? = null
    private val scope = CoroutineScope(Dispatchers.Main + SupervisorJob())
    
    /**
     * 开始监听权限状态
     */
    fun startMonitoring() {
        if (monitorJob?.isActive == true) {
            return
        }
        
        logger.i("PermissionStatusMonitor", "开始监听权限状态")
        
        monitorJob = scope.launch {
            while (isActive) {
                try {
                    // 检查 MediaProjection 有效性
                    mediaProjectionHelper.refreshMediaProjectionIfNeeded()

                    // 检查权限状态
                    permissionManager.checkAllPermissions()

                    // 每3秒检查一次
                    delay(3000)
                } catch (e: Exception) {
                    logger.e("PermissionStatusMonitor", "权限状态检查异常", e)
                    delay(5000) // 出错时延长间隔
                }
            }
        }
    }
    
    /**
     * 停止监听权限状态
     */
    fun stopMonitoring() {
        logger.i("PermissionStatusMonitor", "停止监听权限状态")
        monitorJob?.cancel()
        monitorJob = null
    }
    
    /**
     * 立即检查一次权限状态
     */
    fun checkNow() {
        scope.launch {
            try {
                permissionManager.checkAllPermissions()
            } catch (e: Exception) {
                logger.e("PermissionStatusMonitor", "立即检查权限状态异常", e)
            }
        }
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        stopMonitoring()
        scope.cancel()
    }
}
