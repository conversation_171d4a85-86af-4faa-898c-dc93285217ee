package com.jerome.supernuggetsmaster.utils

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import org.opencv.android.Utils
import org.opencv.core.*
import org.opencv.imgproc.Imgproc
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 图像识别工具类
 * 基于OpenCV实现模板匹配功能，支持分辨率适配
 */
@Singleton
class ImageRecognitionHelper @Inject constructor(
    @ApplicationContext private val context: Context,
    private val logger: Logger
) {
    
    companion object {
        // 基准分辨率 (模板图片的分辨率)
        private const val BASE_WIDTH = 1080
        private const val BASE_HEIGHT = 2340
        
        // 默认置信度阈值
        private const val DEFAULT_CONFIDENCE_THRESHOLD = 0.8f
        
        // 最小和最大置信度阈值
        private const val MIN_CONFIDENCE_THRESHOLD = 0.6f
        private const val MAX_CONFIDENCE_THRESHOLD = 0.95f
    }
    
    private var isOpenCVInitialized = false
    
    /**
     * 图像匹配结果
     */
    data class MatchResult(
        val isMatch: Boolean,
        val confidence: Double,
        val location: Point? = null,
        val adjustedThreshold: Float
    )
    
    /**
     * 分辨率适配信息
     */
    data class ResolutionAdapter(
        val scaleX: Float,
        val scaleY: Float,
        val adjustedThreshold: Float
    )
    
    /**
     * 设置OpenCV初始化状态
     * 由外部统一的初始化管理器调用
     */
    fun setOpenCVInitialized(initialized: Boolean) {
        isOpenCVInitialized = initialized
        logger.i("ImageRecognitionHelper", "OpenCV初始化状态设置为: $initialized")
    }
    
    /**
     * 计算分辨率适配参数
     */
    private fun calculateResolutionAdapter(currentWidth: Int, currentHeight: Int): ResolutionAdapter {
        val scaleX = currentWidth.toFloat() / BASE_WIDTH
        val scaleY = currentHeight.toFloat() / BASE_HEIGHT
        
        // 计算平均缩放比例
        val avgScale = (scaleX + scaleY) / 2f
        
        // 根据缩放比例调整置信度阈值
        // 缩放比例越大，图像可能越模糊，降低阈值
        // 缩放比例越小，图像可能越清晰，提高阈值
        val adjustedThreshold = when {
            avgScale > 1.5f -> DEFAULT_CONFIDENCE_THRESHOLD * 0.85f  // 大屏幕，降低阈值
            avgScale > 1.2f -> DEFAULT_CONFIDENCE_THRESHOLD * 0.9f   // 中大屏幕，略降低阈值
            avgScale < 0.8f -> DEFAULT_CONFIDENCE_THRESHOLD * 1.1f   // 小屏幕，提高阈值
            avgScale < 0.9f -> DEFAULT_CONFIDENCE_THRESHOLD * 1.05f  // 中小屏幕，略提高阈值
            else -> DEFAULT_CONFIDENCE_THRESHOLD                      // 标准屏幕，使用默认阈值
        }.coerceIn(MIN_CONFIDENCE_THRESHOLD, MAX_CONFIDENCE_THRESHOLD)
        
        logger.d("ImageRecognitionHelper", 
            "分辨率适配 - 当前: ${currentWidth}x${currentHeight}, " +
            "基准: ${BASE_WIDTH}x${BASE_HEIGHT}, " +
            "缩放: ${scaleX}x${scaleY}, " +
            "调整后阈值: $adjustedThreshold")
        
        return ResolutionAdapter(scaleX, scaleY, adjustedThreshold)
    }
    
    /**
     * 从assets加载模板图片
     */
    private fun loadTemplateFromAssets(templatePath: String): Bitmap? {
        return try {
            val inputStream = context.assets.open(templatePath)
            val bitmap = BitmapFactory.decodeStream(inputStream)
            inputStream.close()
            
            if (bitmap != null) {
                logger.d("ImageRecognitionHelper", "模板图片加载成功: $templatePath, 尺寸: ${bitmap.width}x${bitmap.height}")
            } else {
                logger.w("ImageRecognitionHelper", "模板图片加载失败: $templatePath")
            }
            
            bitmap
        } catch (e: Exception) {
            logger.e("ImageRecognitionHelper", "加载模板图片异常: $templatePath", e)
            null
        }
    }
    
    /**
     * 执行模板匹配
     */
    fun matchTemplate(
        screenBitmap: Bitmap,
        templatePath: String
    ): MatchResult {
        if (!isOpenCVInitialized) {
            logger.w("ImageRecognitionHelper", "OpenCV未初始化，无法进行图像识别")
            return MatchResult(false, 0.0, null, DEFAULT_CONFIDENCE_THRESHOLD)
        }
        
        return try {
            // 加载模板图片
            val templateBitmap = loadTemplateFromAssets(templatePath)
            if (templateBitmap == null) {
                logger.w("ImageRecognitionHelper", "模板图片加载失败: $templatePath")
                return MatchResult(false, 0.0, null, DEFAULT_CONFIDENCE_THRESHOLD)
            }
            
            // 计算分辨率适配参数
            val adapter = calculateResolutionAdapter(screenBitmap.width, screenBitmap.height)
            
            // 转换为OpenCV Mat
            val screenMat = Mat()
            val templateMat = Mat()
            Utils.bitmapToMat(screenBitmap, screenMat)
            Utils.bitmapToMat(templateBitmap, templateMat)
            
            // 转换为灰度图像以提高匹配效率
            val screenGray = Mat()
            val templateGray = Mat()
            Imgproc.cvtColor(screenMat, screenGray, Imgproc.COLOR_BGR2GRAY)
            Imgproc.cvtColor(templateMat, templateGray, Imgproc.COLOR_BGR2GRAY)
            
            // 如果需要，缩放模板图片以适配当前分辨率
            val scaledTemplate = Mat()
            if (adapter.scaleX != 1.0f || adapter.scaleY != 1.0f) {
                val newSize = Size(
                    (templateGray.cols() * adapter.scaleX).toDouble(),
                    (templateGray.rows() * adapter.scaleY).toDouble()
                )
                Imgproc.resize(templateGray, scaledTemplate, newSize)
                logger.d("ImageRecognitionHelper", 
                    "模板缩放: ${templateGray.cols()}x${templateGray.rows()} -> ${scaledTemplate.cols()}x${scaledTemplate.rows()}")
            } else {
                templateGray.copyTo(scaledTemplate)
            }
            
            // 执行模板匹配
            val result = Mat()
            Imgproc.matchTemplate(screenGray, scaledTemplate, result, Imgproc.TM_CCOEFF_NORMED)
            
            // 找到最佳匹配位置
            val minMaxLocResult = Core.minMaxLoc(result)
            val maxConfidence = minMaxLocResult.maxVal
            val maxLocation = minMaxLocResult.maxLoc
            
            // 判断是否匹配成功
            val isMatch = maxConfidence >= adapter.adjustedThreshold
            
            logger.d("ImageRecognitionHelper", 
                "模板匹配结果 - 模板: $templatePath, " +
                "置信度: $maxConfidence, " +
                "阈值: ${adapter.adjustedThreshold}, " +
                "匹配: $isMatch, " +
                "位置: (${maxLocation.x}, ${maxLocation.y})")
            
            // 释放资源
            screenMat.release()
            templateMat.release()
            screenGray.release()
            templateGray.release()
            scaledTemplate.release()
            result.release()
            templateBitmap.recycle()
            
            MatchResult(
                isMatch = isMatch,
                confidence = maxConfidence,
                location = if (isMatch) maxLocation else null,
                adjustedThreshold = adapter.adjustedThreshold
            )
            
        } catch (e: Exception) {
            logger.e("ImageRecognitionHelper", "模板匹配异常: $templatePath", e)
            MatchResult(false, 0.0, null, DEFAULT_CONFIDENCE_THRESHOLD)
        }
    }
    
    /**
     * 检查OpenCV是否已初始化
     */
    fun isInitialized(): Boolean = isOpenCVInitialized
}
