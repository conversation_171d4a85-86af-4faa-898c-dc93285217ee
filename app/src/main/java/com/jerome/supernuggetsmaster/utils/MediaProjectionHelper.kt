package com.jerome.supernuggetsmaster.utils

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.media.projection.MediaProjection
import android.media.projection.MediaProjectionManager
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.fragment.app.FragmentActivity
import com.jerome.supernuggetsmaster.permission.MediaProjectionManager as AppMediaProjectionManager
import com.jerome.supernuggetsmaster.service.MediaProjectionService
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 媒体投影权限助手 - 简化版
 * 负责请求和管理媒体投影权限
 */
@Singleton
class MediaProjectionHelper @Inject constructor(
    @ApplicationContext private val context: Context,
    private val logger: Logger,
    private val screenshotHelper: ScreenshotHelper
) {

    private var mediaProjectionManager: MediaProjectionManager? = null
    private var mediaProjection: MediaProjection? = null
    
    /**
     * 初始化媒体投影管理器
     */
    fun initializeManager() {
        if (mediaProjectionManager == null) {
            mediaProjectionManager = context.getSystemService(Context.MEDIA_PROJECTION_SERVICE) as MediaProjectionManager
            logger.i("MediaProjectionHelper", "媒体投影管理器初始化完成")
        }
    }
    
    private var permissionCallback: ((Boolean) -> Unit)? = null
    
    /**
     * 请求媒体投影权限 - 简化版
     */
    fun requestPermission() {
        initializeManager()
        val manager = mediaProjectionManager

        if (manager != null) {
            logger.i("MediaProjectionHelper", "开始请求媒体投影权限")
            val intent = manager.createScreenCaptureIntent()
            GlobalMediaProjectionLauncher.launch(intent)
        } else {
            logger.e("MediaProjectionHelper", "媒体投影管理器未初始化")
        }
    }
    
    /**
     * 处理权限请求结果 - 简化版
     */
    fun handlePermissionResult(data: Intent?) {
        if (data == null) {
            logger.w("MediaProjectionHelper", "媒体投影权限请求失败：数据为空")
            return
        }

        try {
            val manager = mediaProjectionManager
            if (manager != null) {
                // 先启动前台服务
                logger.i("MediaProjectionHelper", "启动媒体投影前台服务")
                MediaProjectionService.start(context, null)

                // 获取媒体投影
                mediaProjection = manager.getMediaProjection(Activity.RESULT_OK, data)

                if (mediaProjection != null) {
                    logger.i("MediaProjectionHelper", "媒体投影权限获取成功")

                    // 更新服务中的媒体投影实例
                    MediaProjectionService.setMediaProjection(mediaProjection!!)

                    // 初始化截图助手
                    screenshotHelper.initMediaProjection(mediaProjection!!)

                    // 更新权限状态
                    AppMediaProjectionManager.setInitialized(true)

                    // 设置回调监听
                    mediaProjection?.registerCallback(object : MediaProjection.Callback() {
                        override fun onStop() {
                            super.onStop()
                            logger.i("MediaProjectionHelper", "媒体投影已停止")
                            AppMediaProjectionManager.setInitialized(false)
                            MediaProjectionService.stop(context)
                            mediaProjection = null
                        }
                    }, null)

                } else {
                    logger.e("MediaProjectionHelper", "创建媒体投影失败")
                    MediaProjectionService.stop(context)
                }
            } else {
                logger.e("MediaProjectionHelper", "媒体投影管理器为空")
            }
        } catch (e: Exception) {
            logger.e("MediaProjectionHelper", "处理媒体投影权限结果时发生异常", e)
            MediaProjectionService.stop(context)
        }
    }
    
    /**
     * 检查是否已有权限
     */
    fun hasPermission(): Boolean {
        return MediaProjectionService.isRunning() && AppMediaProjectionManager.isInitialized()
    }
    
    /**
     * 释放媒体投影资源
     */
    fun release() {
        try {
            MediaProjectionService.stop(context)
            mediaProjection?.stop()
            mediaProjection = null
            AppMediaProjectionManager.setInitialized(false)
            screenshotHelper.release()
            logger.i("MediaProjectionHelper", "媒体投影资源已释放")
        } catch (e: Exception) {
            logger.e("MediaProjectionHelper", "释放媒体投影资源时发生异常", e)
        }
    }
    
    /**
     * 获取当前媒体投影实例
     */
    fun getMediaProjection(): MediaProjection? = MediaProjectionService.getMediaProjection() ?: mediaProjection
}

/**
 * 全局媒体投影启动器 - 简化版
 */
object GlobalMediaProjectionLauncher {
    private var launcher: ActivityResultLauncher<Intent>? = null
    private var mediaProjectionHelper: MediaProjectionHelper? = null

    fun initialize(activity: FragmentActivity, helper: MediaProjectionHelper) {
        mediaProjectionHelper = helper
        launcher = activity.registerForActivityResult(
            ActivityResultContracts.StartActivityForResult()
        ) { result ->
            if (result.resultCode == Activity.RESULT_OK) {
                mediaProjectionHelper?.handlePermissionResult(result.data)
            } else {
                mediaProjectionHelper?.handlePermissionResult(null)
            }
        }
    }

    fun launch(intent: Intent) {
        launcher?.launch(intent) ?: run {
            android.util.Log.e("GlobalMediaProjectionLauncher", "启动器未初始化")
        }
    }
}
