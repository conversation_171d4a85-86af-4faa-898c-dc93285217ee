package com.jerome.supernuggetsmaster.utils

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.media.projection.MediaProjection
import android.media.projection.MediaProjectionManager
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.fragment.app.FragmentActivity
import com.jerome.supernuggetsmaster.permission.MediaProjectionManager as AppMediaProjectionManager
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 媒体投影权限助手
 * 负责请求和管理媒体投影权限
 */
@Singleton
class MediaProjectionHelper @Inject constructor(
    @ApplicationContext private val context: Context,
    private val logger: Logger,
    private val screenshotHelper: ScreenshotHelper
) {
    
    private var mediaProjectionManager: MediaProjectionManager? = null
    private var mediaProjection: MediaProjection? = null
    
    /**
     * 初始化媒体投影管理器
     */
    fun initializeManager() {
        if (mediaProjectionManager == null) {
            mediaProjectionManager = context.getSystemService(Context.MEDIA_PROJECTION_SERVICE) as MediaProjectionManager
            logger.i("MediaProjectionHelper", "媒体投影管理器初始化完成")
        }
    }
    
    private var permissionCallback: ((Boolean) -> Unit)? = null
    
    /**
     * 请求媒体投影权限
     */
    fun requestPermission(callback: (Boolean) -> Unit) {
        initializeManager()
        val manager = mediaProjectionManager

        if (manager != null) {
            logger.i("MediaProjectionHelper", "开始请求媒体投影权限")
            permissionCallback = callback
            val intent = manager.createScreenCaptureIntent()
            // 这里需要通过全局的启动器来启动
            GlobalMediaProjectionLauncher.launch(intent)
        } else {
            logger.e("MediaProjectionHelper", "媒体投影管理器未初始化")
            callback(false)
        }
    }
    
    /**
     * 处理权限请求结果
     */
    fun handlePermissionResult(data: Intent?) {
        val callback = permissionCallback
        if (callback == null) {
            logger.w("MediaProjectionHelper", "权限回调为空")
            return
        }

        if (data == null) {
            logger.w("MediaProjectionHelper", "媒体投影权限请求失败：数据为空")
            callback(false)
            return
        }
        try {
            val manager = mediaProjectionManager
            if (manager != null) {
                mediaProjection = manager.getMediaProjection(Activity.RESULT_OK, data)
                
                if (mediaProjection != null) {
                    logger.i("MediaProjectionHelper", "媒体投影权限获取成功")
                    
                    // 初始化截图助手
                    screenshotHelper.initMediaProjection(mediaProjection!!)
                    
                    // 更新权限状态
                    AppMediaProjectionManager.setInitialized(true)
                    
                    // 设置回调监听
                    mediaProjection?.registerCallback(object : MediaProjection.Callback() {
                        override fun onStop() {
                            super.onStop()
                            logger.i("MediaProjectionHelper", "媒体投影已停止")
                            AppMediaProjectionManager.setInitialized(false)
                            mediaProjection = null
                        }
                    }, null)
                    
                    callback(true)
                } else {
                    logger.e("MediaProjectionHelper", "创建媒体投影失败")
                    callback(false)
                }
            } else {
                logger.e("MediaProjectionHelper", "媒体投影管理器为空")
                callback(false)
            }
        } catch (e: Exception) {
            logger.e("MediaProjectionHelper", "处理媒体投影权限结果时发生异常", e)
            callback(false)
        } finally {
            permissionCallback = null
        }
    }
    
    /**
     * 检查是否已有权限
     */
    fun hasPermission(): Boolean {
        return mediaProjection != null && AppMediaProjectionManager.isInitialized()
    }
    
    /**
     * 释放媒体投影资源
     */
    fun release() {
        try {
            mediaProjection?.stop()
            mediaProjection = null
            AppMediaProjectionManager.setInitialized(false)
            screenshotHelper.release()
            logger.i("MediaProjectionHelper", "媒体投影资源已释放")
        } catch (e: Exception) {
            logger.e("MediaProjectionHelper", "释放媒体投影资源时发生异常", e)
        }
    }
    
    /**
     * 获取当前媒体投影实例
     */
    fun getMediaProjection(): MediaProjection? = mediaProjection
}

/**
 * 全局媒体投影启动器
 * 用于在Activity级别管理权限请求
 */
object GlobalMediaProjectionLauncher {
    private var launcher: ActivityResultLauncher<Intent>? = null

    fun initialize(activity: FragmentActivity, mediaProjectionHelper: MediaProjectionHelper) {
        launcher = activity.registerForActivityResult(
            ActivityResultContracts.StartActivityForResult()
        ) { result ->
            if (result.resultCode == Activity.RESULT_OK) {
                mediaProjectionHelper.handlePermissionResult(result.data)
            } else {
                mediaProjectionHelper.handlePermissionResult(null)
            }
        }
    }

    fun launch(intent: Intent) {
        launcher?.launch(intent) ?: run {
            android.util.Log.e("GlobalMediaProjectionLauncher", "启动器未初始化")
        }
    }
}
