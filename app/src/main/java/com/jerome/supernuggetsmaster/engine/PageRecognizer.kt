package com.jerome.supernuggetsmaster.engine

import android.content.Context
import com.jerome.supernuggetsmaster.data.PageRecognitionResult
import com.jerome.supernuggetsmaster.data.ScriptIconInfo
import com.jerome.supernuggetsmaster.data.ScriptPageInfo
import com.jerome.supernuggetsmaster.service.AutomationAccessibilityService
import com.jerome.supernuggetsmaster.utils.Logger
import com.jerome.supernuggetsmaster.utils.ImageRecognitionHelper
import com.jerome.supernuggetsmaster.utils.ScreenshotHelper
import com.jerome.supernuggetsmaster.utils.OpenCVInitializer
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 页面识别器
 * 负责识别当前屏幕是脚本定义的哪个页面
 */
@Singleton
class PageRecognizer @Inject constructor(
    private val context: Context,
    private val logger: Logger,
    private val imageRecognitionHelper: ImageRecognitionHelper,
    private val screenshotHelper: ScreenshotHelper,
    private val openCVInitializer: OpenCVInitializer
) {
    
    /**
     * 页面识别结果包含命中分数
     */
    private data class PageMatchResult(
        val pageInfo: ScriptPageInfo,
        val idMatchCount: Int,
        val tempImgMatchCount: Int,
        val totalScore: Float,
        val imageMatchDetails: List<ImageMatchDetail> = emptyList()
    )

    /**
     * 图像匹配详情
     */
    private data class ImageMatchDetail(
        val templatePath: String,
        val isMatch: Boolean,
        val confidence: Double,
        val adjustedThreshold: Float
    )
    
    /**
     * 识别当前页面
     * 策略：
     * 1. 优先通过ids查找元素
     * 2. 如果ids命中不足一半，结合tempImgs查找
     * 3. 计算综合得分，返回命中率最高的页面
     */
    suspend fun recognizeCurrentPage(pages: List<ScriptPageInfo>): PageRecognitionResult {
        logger.d("PageRecognizer", "开始识别当前页面，候选页面数: ${pages.size}")
        
        try {
            val accessibilityService = AutomationAccessibilityService.getInstance()
            if (accessibilityService == null) {
                logger.w("PageRecognizer", "无障碍服务未启用，无法进行页面识别")
                return PageRecognitionResult(
                    pageInfo = null,
                    confidence = 0.0f
                )
            }
            
            val matchResults = mutableListOf<PageMatchResult>()
            
            // 逐个检查每个页面的匹配度
            for (page in pages) {
                val matchResult = recognizePage(page, accessibilityService)
                if (matchResult.totalScore > 0.0f) {
                    matchResults.add(matchResult)
                }
            }
            
            // 按得分排序，选择得分最高的页面
            val bestMatch = matchResults.maxByOrNull { it.totalScore }
            
            if (bestMatch != null) {
                logger.i("PageRecognizer", 
                    "识别成功 - 页面: ${bestMatch.pageInfo.name}, " +
                    "ID命中: ${bestMatch.idMatchCount}/${bestMatch.pageInfo.ids.size}, " +
                    "图片命中: ${bestMatch.tempImgMatchCount}/${bestMatch.pageInfo.tempImgs.size}, " +
                    "总得分: ${bestMatch.totalScore}")
                
                return PageRecognitionResult(
                    pageInfo = bestMatch.pageInfo,
                    confidence = bestMatch.totalScore
                )
            } else {
                logger.d("PageRecognizer", "未能识别到任何匹配的页面")
                return PageRecognitionResult(
                    pageInfo = null,
                    confidence = 0.0f
                )
            }
            
        } catch (e: Exception) {
            logger.e("PageRecognizer", "页面识别过程中发生异常", e)
            return PageRecognitionResult(
                pageInfo = null,
                confidence = 0.0f
            )
        }
    }
    
    /**
     * 识别单个页面的匹配度
     */
    private suspend fun recognizePage(page: ScriptPageInfo, accessibilityService: AutomationAccessibilityService): PageMatchResult {
        var idMatchCount = 0
        var tempImgMatchCount = 0
        
        // 1. 通过ID查找元素
        for (id in page.ids) {
            if (id.isNotEmpty()) {
                val node = accessibilityService.findNodeById(id)
                if (node != null) {
                    idMatchCount++
                    logger.d("PageRecognizer", "ID匹配成功: $id (页面: ${page.name})")
                } else {
                    logger.d("PageRecognizer", "ID未找到: $id (页面: ${page.name})")
                }
            }
        }
        
        // 2. 计算ID命中率
        val idHitRate = if (page.ids.isNotEmpty()) {
            idMatchCount.toFloat() / page.ids.size
        } else {
            0.0f
        }
        
        // 3. 如果ID命中不足一半且有tempImgs，结合图片识别
        var shouldUseImageRecognition = false
        if (page.tempImgs.isNotEmpty()) {
            if (page.ids.isEmpty() || idHitRate < 0.5f) {
                shouldUseImageRecognition = true
                logger.d("PageRecognizer", "ID命中率不足或无ID，启用图片识别 (页面: ${page.name})")
            }
        }
        
        // 4. 图片识别（真实实现）
        val imageMatchDetails = mutableListOf<ImageMatchDetail>()
        if (shouldUseImageRecognition) {
            for (tempImg in page.tempImgs) {
                if (tempImg.isNotEmpty()) {
                    val matchResult = performImageRecognition(tempImg)
                    imageMatchDetails.add(
                        ImageMatchDetail(
                            templatePath = tempImg,
                            isMatch = matchResult.isMatch,
                            confidence = matchResult.confidence,
                            adjustedThreshold = matchResult.adjustedThreshold
                        )
                    )

                    if (matchResult.isMatch) {
                        tempImgMatchCount++
                        logger.d("PageRecognizer",
                            "图片匹配成功: $tempImg (页面: ${page.name}), " +
                            "置信度: ${matchResult.confidence}, " +
                            "阈值: ${matchResult.adjustedThreshold}")
                    } else {
                        logger.d("PageRecognizer",
                            "图片匹配失败: $tempImg (页面: ${page.name}), " +
                            "置信度: ${matchResult.confidence}, " +
                            "阈值: ${matchResult.adjustedThreshold}")
                    }
                }
            }
        }
        
        // 5. 计算综合得分
        val totalScore = calculateTotalScore(
            idMatchCount, page.ids.size,
            tempImgMatchCount, page.tempImgs.size,
            shouldUseImageRecognition
        )
        
        return PageMatchResult(
            pageInfo = page,
            idMatchCount = idMatchCount,
            tempImgMatchCount = tempImgMatchCount,
            totalScore = totalScore,
            imageMatchDetails = imageMatchDetails
        )
    }
    
    /**
     * 计算页面总得分
     * 权重分配：ID识别权重更高，图片识别作为辅助
     */
    private fun calculateTotalScore(
        idMatchCount: Int, 
        totalIds: Int,
        tempImgMatchCount: Int, 
        totalTempImgs: Int,
        usedImageRecognition: Boolean
    ): Float {
        if (totalIds == 0 && totalTempImgs == 0) {
            return 0.0f
        }
        
        var score = 0.0f
        
        // ID识别得分（权重0.7）
        if (totalIds > 0) {
            val idScore = (idMatchCount.toFloat() / totalIds) * 0.7f
            score += idScore
        }
        
        // 图片识别得分（权重0.3）
        if (usedImageRecognition && totalTempImgs > 0) {
            val imgScore = (tempImgMatchCount.toFloat() / totalTempImgs) * 0.3f
            score += imgScore
        }
        
        // 如果只有ID识别且命中率高，给予额外奖励
        if (totalIds > 0 && !usedImageRecognition) {
            val idHitRate = idMatchCount.toFloat() / totalIds
            if (idHitRate >= 0.8f) {
                score = idHitRate  // 直接使用ID命中率作为得分
            }
        }
        
        // 如果两种识别都使用且都有较好命中率，给予奖励
        if (usedImageRecognition && totalIds > 0 && totalTempImgs > 0) {
            val idHitRate = idMatchCount.toFloat() / totalIds
            val imgHitRate = tempImgMatchCount.toFloat() / totalTempImgs
            if (idHitRate >= 0.3f && imgHitRate >= 0.5f) {
                score += 0.1f  // 组合识别奖励
            }
        }
        
        return score.coerceIn(0.0f, 1.0f)
    }
    
    /**
     * 执行图片识别
     * 基于OpenCV的模板匹配，支持分辨率适配
     */
    private suspend fun performImageRecognition(templatePath: String): ImageRecognitionHelper.MatchResult {
        return try {
            // 检查OpenCV是否已初始化
            if (!openCVInitializer.isInitialized()) {
                logger.w("PageRecognizer", "OpenCV未初始化，无法进行图像识别")
                return ImageRecognitionHelper.MatchResult(
                    isMatch = false,
                    confidence = 0.0,
                    location = null,
                    adjustedThreshold = 0.8f
                )
            }

            // 获取当前屏幕截图
            val screenBitmap = screenshotHelper.captureScreen()
            if (screenBitmap == null) {
                logger.w("PageRecognizer", "获取屏幕截图失败，无法进行图像识别")
                return ImageRecognitionHelper.MatchResult(
                    isMatch = false,
                    confidence = 0.0,
                    location = null,
                    adjustedThreshold = 0.8f
                )
            }

            // 执行模板匹配
            val result = imageRecognitionHelper.matchTemplate(screenBitmap, templatePath)

            // 释放截图资源
            screenBitmap.recycle()

            result

        } catch (e: Exception) {
            logger.e("PageRecognizer", "图像识别过程中发生异常: $templatePath", e)
            ImageRecognitionHelper.MatchResult(
                isMatch = false,
                confidence = 0.0,
                location = null,
                adjustedThreshold = 0.8f
            )
        }
    }

    /**
     * 初始化图像识别组件
     * 需要在使用前调用
     */
    fun initImageRecognition(callback: (Boolean) -> Unit) {
        openCVInitializer.initialize { success ->
            if (success) {
                logger.i("PageRecognizer", "图像识别组件初始化成功")
            } else {
                logger.e("PageRecognizer", "图像识别组件初始化失败")
            }
            callback(success)
        }
    }
} 