package com.jerome.supernuggetsmaster

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.animation.*
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.CheckCircle
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material.icons.filled.Refresh

import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.jerome.supernuggetsmaster.data.ScriptExecutionStatus
import com.jerome.supernuggetsmaster.data.ScriptInfo
import com.jerome.supernuggetsmaster.permission.PermissionManager
import com.jerome.supernuggetsmaster.ui.ScriptViewModel
import com.jerome.supernuggetsmaster.ui.MijiaBottomActionBar
import com.jerome.supernuggetsmaster.ui.MijiaScriptListScreen
import com.jerome.supernuggetsmaster.ui.PermissionSetupScreen
import com.jerome.supernuggetsmaster.ui.theme.SuperNuggetsMasterTheme
import com.jerome.supernuggetsmaster.utils.OpenCVInitializer
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class MainActivity : ComponentActivity() {
    
    @Inject
    lateinit var permissionManager: PermissionManager

    @Inject
    lateinit var openCVInitializer: OpenCVInitializer

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        // 初始化OpenCV
        initializeOpenCV()

        setContent {
            SuperNuggetsMasterTheme {
                MainScreen(permissionManager = permissionManager)
            }
        }
    }

    /**
     * 初始化OpenCV
     */
    private fun initializeOpenCV() {
        openCVInitializer.initialize { success ->
            if (success) {
                // OpenCV初始化成功，可以使用图像识别功能
                android.util.Log.i("MainActivity", "OpenCV初始化成功，图像识别功能可用")
            } else {
                // OpenCV初始化失败，图像识别功能不可用
                android.util.Log.w("MainActivity", "OpenCV初始化失败，图像识别功能不可用")
            }
        }
    }

    override fun onResume() {
        super.onResume()
        // 每次回到前台时检查权限状态
        permissionManager.checkAllPermissions()
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MainScreen(permissionManager: PermissionManager) {
    val scriptViewModel: ScriptViewModel = hiltViewModel()
    val permissionStatus by permissionManager.permissionStatus.collectAsStateWithLifecycle()
    val scripts by scriptViewModel.scripts.collectAsStateWithLifecycle()
    val executionStatus by scriptViewModel.executionStatus.collectAsStateWithLifecycle()
    val currentScript by scriptViewModel.currentScript.collectAsStateWithLifecycle()
    val executionProgress by scriptViewModel.executionProgress.collectAsStateWithLifecycle()
    val selectedScriptCount by scriptViewModel.selectedScriptCount.collectAsStateWithLifecycle()
    
    // 启动时检查权限
    LaunchedEffect(Unit) {
        permissionManager.checkAllPermissions()
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = "SuperNuggets Master",
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onPrimary
                    )
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.primary
                )
            )
        },
        bottomBar = {
            if (permissionStatus.allPermissionsGranted) {
                MijiaBottomActionBar(
                    selectedCount = selectedScriptCount,
                    executionStatus = executionStatus,
                    onStartExecution = { scriptViewModel.startExecution() },
                    onStopExecution = { scriptViewModel.stopExecution() },
                    onSelectAll = { scriptViewModel.toggleSelectAll() },
                    onReset = { scriptViewModel.resetExecution() }
                )
            }
        }
    ) { innerPadding ->
        if (!permissionStatus.allPermissionsGranted) {
            // 权限未完成时显示权限页面
            PermissionSetupScreen(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(innerPadding),
                permissionStatus = permissionStatus,
                permissionManager = permissionManager
            )
        } else {
            // 权限完成后显示脚本列表
            MijiaScriptListScreen(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(innerPadding),
                scripts = scripts,
                executionStatus = executionStatus,
                currentScript = currentScript,
                executionProgress = executionProgress,
                onScriptToggle = { scriptId -> scriptViewModel.toggleScriptSelection(scriptId) }
            )
        }
    }
}

