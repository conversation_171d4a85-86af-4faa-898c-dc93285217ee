# 图像识别功能使用说明

## 概述

本项目已成功集成 OpenCV 图像识别功能，支持基于模板匹配的页面识别，并具备分辨率自适应能力。

## 核心特性

### 1. 分辨率适配
- **基准分辨率**: 1080x2340 (模板图片的标准分辨率)
- **自动缩放**: 根据设备实际分辨率自动调整模板大小
- **动态阈值**: 根据缩放比例自动调整置信度阈值

### 2. 置信度调整策略
```kotlin
// 根据缩放比例调整置信度阈值
val adjustedThreshold = when {
    avgScale > 1.5f -> DEFAULT_CONFIDENCE_THRESHOLD * 0.85f  // 大屏幕，降低阈值
    avgScale > 1.2f -> DEFAULT_CONFIDENCE_THRESHOLD * 0.9f   // 中大屏幕，略降低阈值
    avgScale < 0.8f -> DEFAULT_CONFIDENCE_THRESHOLD * 1.1f   // 小屏幕，提高阈值
    avgScale < 0.9f -> DEFAULT_CONFIDENCE_THRESHOLD * 1.05f  // 中小屏幕，略提高阈值
    else -> DEFAULT_CONFIDENCE_THRESHOLD                      // 标准屏幕，使用默认阈值
}
```

## 使用方法

### 1. 初始化 (已在 MainActivity 中自动完成)
```kotlin
@Inject
lateinit var openCVInitializer: OpenCVInitializer

// 在 onCreate 中初始化
openCVInitializer.initialize { success ->
    if (success) {
        // OpenCV初始化成功，可以使用图像识别功能
    } else {
        // OpenCV初始化失败，图像识别功能不可用
    }
}
```

### 2. 页面识别 (已在 PageRecognizer 中集成)
```kotlin
// PageRecognizer 会自动使用图像识别
val result = pageRecognizer.recognizeCurrentPage(pages)
```

### 3. 模板图片准备
- 将模板图片放置在 `app/src/main/assets/` 目录下
- 建议使用 1080x2340 分辨率的设备截取模板图片
- 支持 JPG、PNG 等常见格式

### 4. 脚本配置示例
```kotlin
@ScriptPage(
    name = "微信首页",
    ids = ["com.tencent.mm:id/home_container"],
    tempImgs = ["wechat/home.jpg"]  // 模板图片路径
)
fun wechatHomePage() {
    // 页面处理逻辑
}
```

## 技术实现

### 核心组件

1. **ImageRecognitionHelper**: 图像识别核心类
   - 模板匹配算法
   - 分辨率适配
   - 置信度计算

2. **ScreenshotHelper**: 屏幕截图工具
   - 媒体投影截图
   - 图像格式转换

3. **OpenCVInitializer**: OpenCV初始化管理
   - 统一初始化流程
   - 避免重复初始化

4. **PageRecognizer**: 页面识别器 (已更新)
   - 集成真实图像识别
   - ID识别 + 图像识别组合策略

### 识别流程

1. **获取屏幕截图**
   ```kotlin
   val screenBitmap = screenshotHelper.captureScreen()
   ```

2. **加载模板图片**
   ```kotlin
   val templateBitmap = loadTemplateFromAssets(templatePath)
   ```

3. **分辨率适配**
   ```kotlin
   val adapter = calculateResolutionAdapter(screenWidth, screenHeight)
   ```

4. **执行模板匹配**
   ```kotlin
   Imgproc.matchTemplate(screenGray, scaledTemplate, result, Imgproc.TM_CCOEFF_NORMED)
   ```

5. **结果判断**
   ```kotlin
   val isMatch = maxConfidence >= adapter.adjustedThreshold
   ```

## 性能优化

### 1. 图像预处理
- 转换为灰度图像提高匹配效率
- 根据需要缩放模板图片

### 2. 资源管理
- 及时释放 OpenCV Mat 对象
- 回收 Bitmap 资源

### 3. 异步处理
- 图像识别在后台线程执行
- 使用协程避免阻塞UI

## 注意事项

### 1. 权限要求
- 需要媒体投影权限进行屏幕截图
- 确保无障碍服务已启用

### 2. 模板图片质量
- 使用清晰、特征明显的图片作为模板
- 避免包含动态变化的内容
- 建议截取具有唯一性的UI元素

### 3. 性能考虑
- 图像识别相对耗时，建议仅在必要时使用
- 优先使用ID识别，图像识别作为辅助

### 4. 调试建议
- 查看日志了解匹配置信度
- 根据实际效果调整模板图片
- 测试不同分辨率设备的兼容性

## 故障排除

### 1. OpenCV初始化失败
- 检查网络连接 (可能需要下载OpenCV Manager)
- 确认设备兼容性
- 查看初始化日志

### 2. 图像识别准确率低
- 检查模板图片质量
- 调整置信度阈值
- 确认屏幕截图功能正常

### 3. 性能问题
- 减少同时进行的图像识别数量
- 优化模板图片大小
- 检查内存使用情况
