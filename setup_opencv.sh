#!/bin/bash

# OpenCV Android SDK 安装脚本
# 下载并配置 OpenCV 4.8.0 Android SDK

echo "开始下载 OpenCV Android SDK..."

# 创建临时目录
mkdir -p temp_opencv
cd temp_opencv

# 下载 OpenCV Android SDK
OPENCV_VERSION="4.8.0"
OPENCV_URL="https://github.com/opencv/opencv/releases/download/${OPENCV_VERSION}/opencv-${OPENCV_VERSION}-android-sdk.zip"

echo "下载 OpenCV ${OPENCV_VERSION} Android SDK..."
curl -L -o opencv-android-sdk.zip "$OPENCV_URL"

if [ $? -ne 0 ]; then
    echo "下载失败，尝试使用备用链接..."
    # 备用下载链接
    OPENCV_URL_ALT="https://sourceforge.net/projects/opencvlibrary/files/4.8.0/opencv-4.8.0-android-sdk.zip/download"
    curl -L -o opencv-android-sdk.zip "$OPENCV_URL_ALT"
fi

# 解压文件
echo "解压 OpenCV SDK..."
unzip -q opencv-android-sdk.zip

# 移动到项目目录
echo "配置 OpenCV 模块..."
cd ..
mv temp_opencv/OpenCV-android-sdk ./opencv

# 清理临时文件
rm -rf temp_opencv

echo "OpenCV SDK 下载完成！"
echo "请运行以下命令完成配置："
echo "1. 将 opencv 模块添加到 settings.gradle.kts"
echo "2. 在 app/build.gradle.kts 中添加依赖"
echo "3. 同步项目"
